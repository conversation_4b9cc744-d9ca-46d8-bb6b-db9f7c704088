#!/usr/bin/env python3
"""
TNGD Backup System Constants

This module contains all constants and configuration values used throughout
the TNGD backup system to eliminate magic numbers and improve maintainability.
"""

from typing import Dict, Any


class BackupConstants:
    """Core backup operation constants."""
    
    # Query and Processing Constants
    DEFAULT_CHUNK_SIZE = 50000  # Default number of rows per chunk
    DEFAULT_TIMEOUT_SECONDS = 1800  # 30 minutes default timeout
    DEFAULT_MAX_RETRIES = 3  # Default number of retry attempts

    # Data Processing Constants
    LARGE_CHUNK_SIZE = 500000  # Large chunk size for file operations
    STREAMING_CHUNK_SIZE = 100000  # Streaming chunk size
    PROGRESS_LOG_INTERVAL = 100000  # Log progress every N rows
    MILESTONE_LOG_INTERVAL = 1000000  # Log milestones every N rows
    HEARTBEAT_LOG_INTERVAL = 10000  # Heartbeat logging interval
    SMALL_DATA_THRESHOLD = 1000  # Threshold for small data logging
    MEDIUM_DATA_THRESHOLD = 100000  # Threshold for medium data logging

    # Query Limits and Validation
    MAX_QUERY_LIMIT = 10000000  # Maximum allowed query limit
    DEFAULT_ROW_ESTIMATE = 10000  # Default row estimate per day
    
    # Storage Constants
    DEFAULT_CHUNK_SIZE_MB = 100  # Default chunk size in MB for uploads
    DEFAULT_MEMORY_THRESHOLD_PERCENT = 75  # Memory usage threshold
    DEFAULT_UPLOAD_TIMEOUT_SECONDS = 1800  # Upload timeout (30 minutes)
    DEFAULT_RETRY_DELAY_SECONDS = 5  # Delay between retries
    
    # Thread Management Constants
    DEFAULT_MAX_THREADS = 8  # Maximum number of threads
    DEFAULT_CLEANUP_INTERVAL_SECONDS = 30  # Thread cleanup interval
    
    # Monitoring Constants
    DEFAULT_MONITOR_INTERVAL_SECONDS = 30  # Resource monitoring interval
    DEFAULT_MAX_HISTORY_ENTRIES = 100  # Maximum health history entries
    MEMORY_CHECK_INTERVAL_SECONDS = 30  # Memory check throttle interval
    
    # Connection Pool Constants
    DEFAULT_CONNECTION_POOL_SIZE = 5  # OSS connection pool size
    
    # Performance Thresholds
    CPU_WARNING_THRESHOLD = 80.0  # CPU usage warning threshold (%)
    CPU_CRITICAL_THRESHOLD = 95.0  # CPU usage critical threshold (%)
    MEMORY_WARNING_THRESHOLD = 75.0  # Memory usage warning threshold (%)
    MEMORY_CRITICAL_THRESHOLD = 90.0  # Memory usage critical threshold (%)
    THREAD_WARNING_THRESHOLD = 1000  # Thread count warning threshold
    THREAD_CRITICAL_THRESHOLD = 1500  # Thread count critical threshold

    # Emergency Resource Management
    EMERGENCY_THREAD_THRESHOLD = 2000  # Emergency thread cleanup threshold
    EMERGENCY_MEMORY_THRESHOLD = 95.0  # Emergency memory cleanup threshold
    FORCE_CLEANUP_INTERVAL_SECONDS = 60  # Force cleanup every minute under pressure
    PROCESS_CPU_THRESHOLD = 50.0  # CPU threshold for individual process cleanup (%)
    PROCESS_MEMORY_THRESHOLD_MB = 500  # Memory threshold for individual process cleanup (MB)

    # Byte Conversion Constants
    BYTES_PER_KB = 1024  # Bytes in a kilobyte
    BYTES_PER_MB = 1024 * 1024  # Bytes in a megabyte
    BYTES_PER_GB = 1024 * 1024 * 1024  # Bytes in a gigabyte

    # Time Conversion Constants
    SECONDS_PER_MINUTE = 60  # Seconds in a minute
    SECONDS_PER_HOUR = 3600  # Seconds in an hour
    MINUTES_PER_HOUR = 60  # Minutes in an hour

    # Query and Processing Timeouts
    SHORT_TIMEOUT_SECONDS = 15  # Short timeout for quick operations
    MEDIUM_TIMEOUT_SECONDS = 60  # Medium timeout for standard operations
    LONG_TIMEOUT_SECONDS = 300  # Long timeout for complex operations
    TABLE_CHECK_TIMEOUT_SECONDS = 60  # Timeout for table existence checks

    # Heartbeat and Monitoring Intervals
    HEARTBEAT_CHECK_INTERVAL_SECONDS = 15  # Heartbeat check interval
    INACTIVITY_WARNING_SECONDS = 60  # Log warning after this much inactivity
    INACTIVITY_CRITICAL_SECONDS = 120  # Critical inactivity threshold
    PROGRESS_LOG_INTERVAL_SECONDS = 60  # Log progress every minute

    # Thread Pool Settings
    DEFAULT_THREAD_POOL_SIZE = 2  # Default thread pool size for queries
    MAX_THREAD_POOL_SIZE = 4  # Maximum thread pool size

    # Data Processing Thresholds
    LARGE_DATASET_THRESHOLD = 50000  # Threshold for large dataset logging
    SAMPLE_SIZE_FOR_COUNT = 100  # Sample size for count estimation
    CHUNK_PROGRESS_LOG_INTERVAL = 50  # Log progress every N chunks

    # Retry and Backoff Settings
    EXPONENTIAL_BACKOFF_BASE = 2  # Base for exponential backoff
    MAX_RETRY_DELAY_SECONDS = 120  # Maximum retry delay (2 minutes)
    NETWORK_ERROR_BASE_DELAY = 5  # Base delay for network errors
    STANDARD_RETRY_BASE_DELAY = 30  # Standard retry base delay
    JITTER_MIN = 0.8  # Minimum jitter multiplier
    JITTER_MAX = 1.2  # Maximum jitter multiplier

    # String and Query Limits
    MAX_TABLE_NAME_LENGTH = 255  # Maximum table name length
    QUERY_PREVIEW_LENGTH = 100  # Length for query preview in logs
    QUERY_ERROR_PREVIEW_LENGTH = 50  # Length for query preview in errors
    MAX_PROBLEMATIC_COLUMNS_TO_LOG = 5  # Max problematic columns to log

    # Percentage Calculations
    PERCENT_MULTIPLIER = 100  # Multiplier for percentage calculations
    CHUNK_SIZE_REDUCTION_FACTOR = 0.8  # Factor to reduce chunk size under pressure

    # Conservative Estimates
    CONSERVATIVE_ROW_ESTIMATE = 1000  # Conservative row count estimate

    # Future Processing Timeouts
    FUTURE_RESULT_TIMEOUT = 5.0  # Timeout for future results
    HEARTBEAT_FUTURE_TIMEOUT = 2.0  # Timeout for heartbeat futures

    # Note: Duplicate constants removed - using values from "Data Processing Constants" section above


class FileConstants:
    """File and path related constants."""
    
    # File Extensions
    ARCHIVE_EXTENSION = ".tar.gz"
    JSON_EXTENSION = ".json"
    LOG_EXTENSION = ".log"
    
    # Directory Names
    DATA_DIR = "data"
    LOGS_DIR = "logs"
    TEMP_DIR = "temp"
    EXPORTS_DIR = "exports"
    CHECKPOINTS_DIR = "checkpoints"
    CONFIG_DIR = "config"
    
    # File Naming Patterns
    BACKUP_ID_FORMAT = "backup_{timestamp}"
    LOG_FILE_FORMAT = "tngd_backup_{date}.log"
    ARCHIVE_NAME_FORMAT = "{table}_{date}.tar.gz"


class ConfigConstants:
    """Configuration related constants."""
    
    # Default Configuration Files
    DEFAULT_CONFIG_FILE = "config/default.json"
    PRODUCTION_CONFIG_FILE = "config/production.json"
    LARGE_DATASET_CONFIG_FILE = "config/large_dataset.json"
    TABLES_CONFIG_FILE = "config/tables.json"
    
    # Environment Variables
    DEVO_API_KEY_ENV = "DEVO_API_KEY"
    DEVO_API_SECRET_ENV = "DEVO_API_SECRET"
    OSS_ACCESS_KEY_ID_ENV = "OSS_ACCESS_KEY_ID"
    OSS_ACCESS_KEY_SECRET_ENV = "OSS_ACCESS_KEY_SECRET"
    OSS_ENDPOINT_ENV = "OSS_ENDPOINT"
    OSS_BUCKET_NAME_ENV = "OSS_BUCKET_NAME"


class ErrorConstants:
    """Error handling and retry constants."""
    
    # Retry Strategy
    EXPONENTIAL_BACKOFF_BASE = 2  # Base for exponential backoff
    MAX_BACKOFF_SECONDS = 300  # Maximum backoff time (5 minutes)
    
    # Error Categories
    RETRYABLE_ERRORS = [
        "ConnectionError",
        "TimeoutError", 
        "TemporaryFailure",
        "ServiceUnavailable"
    ]
    
    NON_RETRYABLE_ERRORS = [
        "AuthenticationError",
        "PermissionError",
        "InvalidConfiguration",
        "FileNotFound"
    ]


class SystemConstants:
    """System and version constants."""
    
    # Version Information
    VERSION = "2.0.0"
    SYSTEM_NAME = "TNGD Backup System"
    
    # Logging Configuration
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # Date and Time Formats
    DATE_FORMAT_INPUT = "%Y-%m-%d"
    TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
    ISO_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S"


# Convenience function to get all constants as a dictionary
def get_all_constants() -> Dict[str, Any]:
    """
    Get all constants as a nested dictionary for easy access.
    
    Returns:
        Dictionary containing all constant values organized by category
    """
    return {
        "backup": {
            attr: getattr(BackupConstants, attr)
            for attr in dir(BackupConstants)
            if not attr.startswith('_')
        },
        "file": {
            attr: getattr(FileConstants, attr)
            for attr in dir(FileConstants)
            if not attr.startswith('_')
        },
        "config": {
            attr: getattr(ConfigConstants, attr)
            for attr in dir(ConfigConstants)
            if not attr.startswith('_')
        },
        "error": {
            attr: getattr(ErrorConstants, attr)
            for attr in dir(ErrorConstants)
            if not attr.startswith('_')
        },
        "system": {
            attr: getattr(SystemConstants, attr)
            for attr in dir(SystemConstants)
            if not attr.startswith('_')
        }
    }
